// 全局变量定义
let submitTime = 4100; // 交卷时间控制
let reTryTime = 2100; // 重考,视频进入考试延时控制
let randomX = 5000; // 随机时间范围
let autoAnswer = true; // 自动答题功能开关
let autoPlay = true; // 自动播放功能开关
let autoSkipVideo = true; // 自动跳过视频功能开关
let vSpeed = 3; // 默认播放速度

// 重试控制
const MAX_RETRY_COUNT = 10; // 最大重试次数
const RETRY_COUNT_KEY = "JJ_RetryCount"; // 重试计数器存储键

// 记录字段
const keyTest = "JJ_Test";
const keyResult = "JJ_Result";
const keyThisTitle = "JJ_ThisTitle";
const keyTestAnswer = "JJ_TestAnswer";
const keyRightAnswer = "JJ_RightAnswer";
const keyAllAnswer = "JJ_AllAnswer";

// 页面判别
const urlInfos = window.location.href.split("/");
const urlTip = urlInfos[urlInfos.length - 1].split("?")[0];
let clock = null;

// 功能开关
let autoAnswer = true;
let autoPlay = true;
let autoSkipVideo = true;
let vSpeed = 3.0;

/**
 * 初始化函数，根据当前页面类型执行相应操作
 */
function initialize() {
    // 从Chrome存储中加载设置
    chrome.storage.local.get([
        'autoAnswer', 
        'autoPlay', 
        'playbackRate',
        'autoSkipVideo'
    ], function(result) {
        // 设置功能开关和播放速度
        autoAnswer = result.autoAnswer !== undefined ? result.autoAnswer : true;
        autoPlay = result.autoPlay !== undefined ? result.autoPlay : true;
        vSpeed = result.playbackRate ? parseFloat(result.playbackRate) : 3; // 确保是数字类型
        autoSkipVideo = result.autoSkipVideo !== undefined ? result.autoSkipVideo : true;
        console.log("从存储加载播放速度：" + vSpeed + "x");
        
        // 根据页面类型执行相应操作
        if (urlTip == "exam.aspx") { // 考试页面
            console.log("当前任务: 华医考试");
            if (autoAnswer) {
                doTest();
            } else {
                console.log("自动答题功能已关闭");
            }
        } else if (urlTip == "exam_result.aspx") { // 考试结果页面
            console.log("当前任务: 华医考试结果审核");
            if (autoAnswer) {
                doResult();
            } else {
                console.log("自动答题功能已关闭");
            }
        }
    });
}

/**
 * 处理考试页面
 */
function doTest() {
    console.log("开始处理考试页面");

    // 检查是否是新的考试开始，如果URL参数发生变化，清除重试计数器
    const currentUrl = window.location.href;
    const lastExamUrl = localStorage.getItem("JJ_LastExamUrl");

    if (lastExamUrl !== currentUrl) {
        console.log("检测到新的考试，清除重试计数器");
        localStorage.removeItem(RETRY_COUNT_KEY);
        localStorage.setItem("JJ_LastExamUrl", currentUrl);
    }

    // 获取现有数据
    let questions = JSON.parse(localStorage.getItem(keyTest)) || {};
    let qRightAnswer = JSON.parse(localStorage.getItem(keyRightAnswer)) || {};
    
    // 如果正确答案为空，尝试从历史记录加载
    if (Object.keys(qRightAnswer).length === 0) {
        console.log("正确答案库为空，尝试从历史记录加载");
        qRightAnswer = LoadRightAnwser();
    }
    
    // 尝试加载备份的考试结果中的正确答案
    try {
        const examResultsBackup = JSON.parse(localStorage.getItem("JJ_ExamResultsBackup")) || {};
        if (Object.keys(examResultsBackup).length > 0) {
            console.log(`从备份中加载 ${Object.keys(examResultsBackup).length} 个考试结果`);
            // 将备份中的答案合并到正确答案库中
            for (const q in examResultsBackup) {
                const normalizedQ = q.replace(/\s+/g, "");
                qRightAnswer[normalizedQ] = examResultsBackup[q];
            }
        }
    } catch (error) {
        console.log("加载备份考试结果失败：", error);
    }
    
    console.log(`已加载 ${Object.keys(qRightAnswer).length} 个历史正确答案`);
    
    // 存储本次测试的答案
    let qTestAnswer = {};
    
    // 获取所有问题表格
    const allQuestions = document.querySelectorAll("table[class='tablestyle']");
    if (!allQuestions || allQuestions.length === 0) {
        console.log("警告：未找到任何问题，请检查页面结构");
        return;
    }
    
    console.log(`找到 ${allQuestions.length} 个问题`);
    
    // 遍历每个问题表格
    for (let i = 0; i < allQuestions.length; i++) {
        const question = allQuestions[i];
        try {
            // 获取题目元素
            const qNameElement = question.querySelector('.q_name');
            if (!qNameElement) {
                console.log(`问题 ${i+1} 缺少 .q_name 元素`);
                continue;
            }
            
            // 提取题目文本：使用正则表达式去除题号（支持1-2位数字+标点）
            const qText = qNameElement.innerText.replace(/^\d{1,2}[、.)]\s*/, "").replace(/\s+/g, "");
            if (!qText || qText.length < 5) {
                console.log(`问题 ${i+1} 文本异常："${qText}"`);
                continue;
            }
            
            // 获取当前问题表格的选项容器
            const optionContainer = question.querySelector('tbody');
            if (!optionContainer) {
                console.log(`问题 ${i+1} 缺少选项容器`);
                continue;
            }
            
            // 获取所有选项标签
            const optionLabels = optionContainer.querySelectorAll('label');
            if (!optionLabels || optionLabels.length === 0) {
                console.log(`问题 ${i+1} 没有选项`);
                continue;
            }
            
            // 提取选项文本（去除选项字母和标点）
            const optionTexts = [];
            optionLabels.forEach(label => {
                // 使用正则提取选项文本（去除"A、"等前缀）
                const text = label.innerText.replace(/^[A-Z][、.)]\s*/, "").trim();
                optionTexts.push(text);
            });
            
            // 检查是否有已知正确答案
            let hasKnownAnswer = false;
            let knownAnswer = null;
            
            // 直接匹配
            if (qRightAnswer.hasOwnProperty(qText)) {
                hasKnownAnswer = true;
                knownAnswer = qRightAnswer[qText];
            } else {
                // 尝试标准化后匹配（去除所有空格）
                const normalizedQText = qText.replace(/\s+/g, "");
                for (const q in qRightAnswer) {
                    const normalizedQ = q.replace(/\s+/g, "");
                    if (normalizedQ === normalizedQText) {
                        hasKnownAnswer = true;
                        knownAnswer = qRightAnswer[q];
                        break;
                    }
                }
            }
            
            if (hasKnownAnswer && knownAnswer) {
                console.log(`问题 "${qText.substring(0, 20)}..." 有已知正确答案: ${knownAnswer}`);
                
                // 在选项中查找匹配项（使用增强的模糊匹配）
                let matchIndex = -1;
                
                // 1. 首先尝试精确匹配
                for (let j = 0; j < optionTexts.length; j++) {
                    if (optionTexts[j].trim() === knownAnswer.trim()) {
                        matchIndex = j;
                        console.log(`精确匹配成功：选项 ${j+1}`);
                        break;
                    }
                }
                
                // 2. 如果精确匹配失败，尝试模糊匹配
                if (matchIndex === -1) {
                    for (let j = 0; j < optionTexts.length; j++) {
                        if (fuzzyMatch(optionTexts[j], knownAnswer)) {
                            matchIndex = j;
                            console.log(`模糊匹配成功：选项 ${j+1}`);
                            break;
                        }
                    }
                }
                
                // 3. 如果模糊匹配失败，尝试包含匹配
                if (matchIndex === -1) {
                    const normalizedAnswer = knownAnswer.replace(/[\s.,，。、；;:：!！?？()（）\[\]【】]/g, "").toLowerCase();
                    
                    for (let j = 0; j < optionTexts.length; j++) {
                        const normalizedText = optionTexts[j].replace(/[\s.,，。、；;:：!！?？()（）\[\]【】]/g, "").toLowerCase();
                        
                        if (normalizedText.includes(normalizedAnswer) || normalizedAnswer.includes(normalizedText)) {
                            matchIndex = j;
                            console.log(`包含匹配成功：选项 ${j+1}`);
                            break;
                        }
                    }
                }
                
                if (matchIndex >= 0) {
                    optionLabels[matchIndex].click();
                    qTestAnswer[qText] = optionTexts[matchIndex];
                    console.log(`选择已知正确答案：选项 ${matchIndex+1}`);
                } else {
                    console.log(`问题 "${qText.substring(0, 20)}..." 的正确答案不在选项中，尝试选择第一个选项`);
                    optionLabels[0].click();
                    qTestAnswer[qText] = optionTexts[0];
                }
            } else {
                // 未知答案，使用历史记录或默认选择A
                let choiceIndex = 0; // 默认选择第一个选项（A）
                if (questions.hasOwnProperty(qText)) {
                    // 获取下一个选项（循环选择）
                    const currentChoice = questions[qText];
                    const nextChoice = getNextChoice(currentChoice);
                    questions[qText] = nextChoice;
                    choiceIndex = getChoiceCode(nextChoice);
                    
                    // 确保索引在选项范围内
                    if (choiceIndex >= optionLabels.length) {
                        choiceIndex = 0;
                        questions[qText] = 'A'; // 重置为A
                    }
                } else {
                    // 新题，初始化为A
                    questions[qText] = 'A';
                }
                
                // 点击选项
                if (choiceIndex < optionLabels.length) {
                    optionLabels[choiceIndex].click();
                    qTestAnswer[qText] = optionTexts[choiceIndex];
                    console.log(`未知答案，选择选项 ${choiceIndex+1} (${questions[qText]})`);
                } else {
                    console.log(`问题 "${qText.substring(0, 20)}..." 的选项索引超出范围，选择第一个选项`);
                    optionLabels[0].click();
                    qTestAnswer[qText] = optionTexts[0];
                }
            }
        } catch (error) {
            console.log(`处理问题 ${i+1} 时出错：${error}`);
        }
    }
    
    // 新增模糊匹配函数
    function fuzzyMatch(text, pattern) {
        // 标准化文本：移除所有空格和标点
        const normalize = (str) => str.replace(/[\s.,，。、；;:：!！?？()（）\[\]【】]/g, "").toLowerCase();
        
        const normalizedText = normalize(text);
        const normalizedPattern = normalize(pattern);
        
        // 1. 完全匹配
        if (normalizedText === normalizedPattern) return true;
        
        // 2. 包含匹配
        if (normalizedText.includes(normalizedPattern)) return true;
        if (normalizedPattern.includes(normalizedText)) return true;
        
        // 3. 相似度匹配（简单版）
        const minLength = Math.min(normalizedText.length, normalizedPattern.length);
        let matchCount = 0;
        
        for (let i = 0; i < minLength; i++) {
            if (normalizedText[i] === normalizedPattern[i]) matchCount++;
        }
        
        return matchCount / minLength >= 0.8; // 80%相似度
    }

    // 检查是否有答案被记录
    if (Object.keys(qTestAnswer).length === 0) {
        console.log("警告：未记录任何答案，请检查页面结构或脚本逻辑");
    } else {
        console.log(`成功记录 ${Object.keys(qTestAnswer).length} 个答案`);
    }

    // 存储相关记录
    localStorage.setItem(keyTest, JSON.stringify(questions));
    localStorage.setItem(keyTestAnswer, JSON.stringify(qTestAnswer));

    // 延时提交答案
    setTimeout(function () {
        const submitButton = document.querySelector("#btn_submit");
        if (submitButton) {
            console.log("点击提交按钮");
            submitButton.click();
        } else {
            console.log("警告：未找到提交按钮");
        }
    }, (submitTime + Math.ceil(Math.random() * randomX)));
}

/**
 * 处理考试结果页面
 * 提取所有正确答案并保存，无论考试是否通过
 */
function doResult() {
    console.log("开始处理考试结果页面");

    // 首先检查是否需要立即重新考试（页面加载时的快速检测）
    if (detectExamFailure()) {
        console.log("检测到考试未通过，准备重新考试");

        // 给页面一些时间完全加载，然后重新考试
        setTimeout(function() {
            retryExam();
        }, 1000);

        // 继续执行答案提取逻辑，以便保存正确答案
    }

    // 解析考试结果
    const examResults = {};
    const correctAnswers = {};
    
    // 获取所有问题项
    const questionItems = document.querySelectorAll(".state_cour_lis");
    if (!questionItems || questionItems.length === 0) {
        console.log("警告：未找到任何问题项，请检查页面结构");
        return;
    }
    
    console.log(`找到 ${questionItems.length} 个问题结果`);
    
    // 遍历每个问题项
    questionItems.forEach((item, index) => {
        try {
            // 获取问题文本
            const questionElement = item.querySelector("p.state_lis_text");
            if (!questionElement) {
                console.log(`问题 ${index+1} 缺少问题文本元素`);
                return;
            }
            
            const question = questionElement.getAttribute("title");
            if (!question) {
                console.log(`问题 ${index+1} 缺少问题文本`);
                return;
            }
            
            // 标准化问题文本，移除多余空格
            const normalizedQ = question.replace(/\s+/g, "");
            
            // 获取答案文本
            const answerElements = item.querySelectorAll("p.state_lis_text");
            if (answerElements.length < 2) {
                console.log(`问题 ${index+1} 缺少答案元素`);
                return;
            }
            
            const answer = answerElements[1].getAttribute("title");
            if (!answer) {
                console.log(`问题 ${index+1} 缺少答案文本`);
                return;
            }
            
            // 清理答案文本（移除【您的答案：】等前缀）
            const cleanAnswer = answer.replace(/^【您的答案：\s*|\s*】$/g, "").trim();
            if (!cleanAnswer) {
                console.log(`问题 ${index+1} 答案文本为空`);
                return;
            }
            
            // 记录所有答案
            examResults[normalizedQ] = cleanAnswer;
            
            // 检查该题是否正确（图片为bar_img表示正确）
            const imgElement = item.querySelector("img");
            if (imgElement && imgElement.src.includes("bar_img")) {
                correctAnswers[normalizedQ] = cleanAnswer;
                console.log(`记录正确答案 [${index+1}]: ${question.substring(0, 30)}... => ${cleanAnswer}`);
            } else {
                console.log(`记录错误答案 [${index+1}]: ${question.substring(0, 30)}...`);
            }
        } catch (error) {
            console.log(`处理问题结果 ${index+1} 时出错：${error}`);
        }
    });
    
    // 存储到localStorage
    if (Object.keys(examResults).length > 0) {
        localStorage.setItem("JJ_ExamResults", JSON.stringify(examResults));
        console.log(`成功记录 ${Object.keys(examResults).length} 个题目答案`);
        
        // 将正确答案单独存储为备份
        if (Object.keys(correctAnswers).length > 0) {
            localStorage.setItem("JJ_ExamResultsBackup", JSON.stringify(correctAnswers));
            console.log(`已备份 ${Object.keys(correctAnswers).length} 个正确答案`);
        }
    } else {
        console.log("未找到可记录的题目答案");
    }
    
    // 将考试结果发送到background存储（带重试机制）
    if (Object.keys(examResults).length > 0) {
        const MAX_RETRIES = 3;
        let retryCount = 0;
        
        const sendResults = () => {
            try {
                chrome.runtime.sendMessage({
                    action: "recordExamResults",
                    results: examResults
                }, function(response) {
                    // 检查是否有运行时错误
                    if (chrome.runtime.lastError) {
                        const errorMsg = chrome.runtime.lastError.message;
                        retryCount++;
                        
                        // 检查是否是扩展上下文失效错误
                        if (errorMsg.includes("Extension context invalidated") || 
                            errorMsg.includes("disconnected port")) {
                            console.error("扩展上下文已失效，无法发送消息。请刷新页面或重启扩展。");
                            // 将结果保存到localStorage作为备份
                            localStorage.setItem("JJ_ExamResultsBackup", JSON.stringify(correctAnswers));
                            console.log("已将考试结果备份到本地存储");
                            return;
                        }
                        
                        if (retryCount <= MAX_RETRIES) {
                            console.error(`考试结果记录失败，正在重试 (${retryCount}/${MAX_RETRIES})...错误: ${errorMsg}`);
                            setTimeout(sendResults, 1000 * retryCount); // 指数退避
                        } else {
                            console.error("考试结果记录失败，已达最大重试次数。错误详情：", errorMsg);
                            // 将结果保存到localStorage作为备份
                            localStorage.setItem("JJ_ExamResultsBackup", JSON.stringify(correctAnswers));
                            console.log("已将考试结果备份到本地存储");
                        }
                        return;
                    }
                    
                    if (response && response.success) {
                        console.log(`成功记录 ${Object.keys(examResults).length} 个考试结果到后台存储`);
                    } else {
                        retryCount++;
                        
                        if (retryCount <= MAX_RETRIES) {
                            console.error(`考试结果记录失败，正在重试 (${retryCount}/${MAX_RETRIES})...`);
                            setTimeout(sendResults, 1000 * retryCount); // 指数退避
                        } else {
                            console.error("考试结果记录失败，已达最大重试次数。");
                            // 将结果保存到localStorage作为备份
                            localStorage.setItem("JJ_ExamResultsBackup", JSON.stringify(correctAnswers));
                            console.log("已将考试结果备份到本地存储");
                        }
                    }
                });
            } catch (error) {
                console.error("发送消息时发生异常:", error.message);
                // 将结果保存到localStorage作为备份
                localStorage.setItem("JJ_ExamResultsBackup", JSON.stringify(correctAnswers));
                console.log("已将考试结果备份到本地存储");
            }
        };
        
        sendResults();
    }

    // 原有代码继续执行
    const res = $(".tips_text")[0]?.innerText || "";
    const dds = $(".state_cour_lis");

    localStorage.removeItem(keyResult); // 移除错题表缓存

    // 无论考试是否通过，都保存正确答案到答案库
    if (Object.keys(correctAnswers).length > 0) {
        // 获取现有正确答案库
        const qRightAnswer = JSON.parse(localStorage.getItem(keyRightAnswer)) || {};
        const beforeCount = Object.keys(qRightAnswer).length;

        // 将本次正确答案添加到答案库
        for (const q in correctAnswers) {
            qRightAnswer[q] = correctAnswers[q];
        }

        // 保存更新后的答案库
        localStorage.setItem(keyRightAnswer, JSON.stringify(qRightAnswer));

        const afterCount = Object.keys(qRightAnswer).length;
        console.log(`正确答案库：从 ${beforeCount} 题更新到 ${afterCount} 题（添加了 ${Object.keys(correctAnswers).length} 个正确答案）`);
    }

    // 使用增强的考试状态检测
    const examPassed = res == "考试通过" || res == "考试通过！" || res == "完成项目学习可以申请学分了";
    const examFailed = detectExamFailure();

    if (examPassed && !examFailed) { // 考试通过
        console.log("考试通过");

        // 清除重试计数器
        localStorage.removeItem(RETRY_COUNT_KEY);
        console.log("考试通过，已清除重试计数器");

        saveRightAnwser(); // 记录最后一次答对的题目
        SaveAllAnwser(); // 存储所有记录的答案

        // 不再清理答案，保留所有记录的正确答案
        // cleanKeyStorage();

        // 自动进入下一节课
        if (autoPlay) {
            const next = document.getElementsByClassName("state_lis_btn state_lis_han")[0];
            if (next) {
                setTimeout(function () {
                    next.click();
                }, 1000);
            }
        }
    } else if (examFailed || (!examPassed && res.includes("未通过"))) { // 考试未通过
        console.log("考试未通过，开始处理重新考试流程");

        // 更新页面提示信息
        const tipsElement = document.querySelector("p[class='tips_text']") || document.querySelector(".tips_text");
        if (tipsElement) {
            tipsElement.innerText = "本次未通过，正在尝试更换答案\r\n（此为正常现象，脚本几秒后刷新，请勿操作）";
        }

        let qWrong = {};
        for (let i = 0; i < dds.length; ++i) {
            const imgElement = dds[i].querySelector("img");
            if (imgElement && !imgElement.src.includes("bar_img")) { // 错误的题目
                const questionElement = dds[i].querySelector("p");
                if (questionElement && questionElement.title) {
                    qWrong[questionElement.title.replace(/\s*/g, "")] = i;
                }
            }
        }

        console.log(`找到 ${Object.keys(qWrong).length} 个错题`);

        // 无论是否有错题，都保存当前的答案并重新考试
        localStorage.setItem(keyResult, JSON.stringify(qWrong));

        saveRightAnwser(); // 仍然调用原有的保存答案函数
        SaveAllAnwser(); // 存储所有记录的答案，即使未通过考试

        // 增强的重新考试逻辑
        retryExam();
    } else {
        // 状态不明确，记录日志并尝试重新考试
        console.log(`考试状态不明确：res="${res}", examPassed=${examPassed}, examFailed=${examFailed}`);
        console.log("为安全起见，尝试重新考试");

        saveRightAnwser();
        SaveAllAnwser();
        retryExam();
    }
}

/**
 * 保存所有答案
 * 将正确答案合并到历史答案库中，按章节分类存储
 */
function SaveAllAnwser() {
    // 加载所有答案
    let qAllAnswer = JSON.parse(localStorage.getItem(keyAllAnswer)) || {};
    
    // 加载正确答案
    let qRightAnswer = JSON.parse(localStorage.getItem(keyRightAnswer)) || {};
    
    // 尝试加载备份的考试结果中的正确答案
    try {
        const backupAnswers = JSON.parse(localStorage.getItem("JJ_ExamResultsBackup")) || {};
        if (Object.keys(backupAnswers).length > 0) {
            console.log(`从备份中加载 ${Object.keys(backupAnswers).length} 个正确答案`);
            // 将备份中的答案合并到正确答案库中
            for (const q in backupAnswers) {
                const normalizedQ = q.replace(/\s+/g, "");
                qRightAnswer[normalizedQ] = backupAnswers[q];
            }
            
            // 更新正确答案库
            localStorage.setItem(keyRightAnswer, JSON.stringify(qRightAnswer));
            console.log(`已更新正确答案库，当前共有 ${Object.keys(qRightAnswer).length} 个答案`);
        }
    } catch (error) {
        console.log("加载备份考试结果失败：", error);
    }
    
    // 如果正确答案为空，直接返回
    if (Object.keys(qRightAnswer).length === 0) {
        console.log("没有正确答案可以保存");
        return;
    }
    
    // 获取当前章节标题
    const chapterTitle = document.querySelector('.chapter_title')?.innerText || '';
    
    // 如果没有章节标题，使用默认标题
    const title = chapterTitle || "未知章节";
    
    // 初始化章节答案
    if (!qAllAnswer[title]) {
        qAllAnswer[title] = {};
        console.log(`创建新章节 "${title}" 的答案库`);
    }
    
    // 合并答案
    let addedCount = 0;
    for (const q in qRightAnswer) {
        // 标准化问题文本
        const normalizedQ = q.replace(/\s+/g, "");
        
        // 如果答案不存在或不同，则更新
        if (!qAllAnswer[title][normalizedQ] || qAllAnswer[title][normalizedQ] !== qRightAnswer[q]) {
            qAllAnswer[title][normalizedQ] = qRightAnswer[q];
            addedCount++;
        }
    }
    
    // 保存所有答案
    localStorage.setItem(keyAllAnswer, JSON.stringify(qAllAnswer));
    
    // 计算总答案数量
    let totalCount = 0;
    for (const chapter in qAllAnswer) {
        totalCount += Object.keys(qAllAnswer[chapter]).length;
    }
    
    console.log(`已保存 ${addedCount} 个新答案到章节 "${title}"，该章节共 ${Object.keys(qAllAnswer[title]).length} 个答案，总共 ${totalCount} 个答案`);
}

/**
 * 加载历史题目答案
 * @returns {Object} 历史答案对象
 */
function LoadRightAnwser() {
    // 从多个来源加载答案
    const qAllAnswer = JSON.parse(localStorage.getItem(keyAllAnswer)) || {};
    const qRightAnswer = JSON.parse(localStorage.getItem(keyRightAnswer)) || {};
    const qErrorTest = JSON.parse(localStorage.getItem(keyResult)) || {};
    const qTitle = JSON.parse(localStorage.getItem(keyThisTitle)) || "没有记录到章节名称";
    
    // 尝试加载备份的考试结果中的正确答案
    let backupAnswers = {};
    try {
        backupAnswers = JSON.parse(localStorage.getItem("JJ_ExamResultsBackup")) || {};
        if (Object.keys(backupAnswers).length > 0) {
            console.log(`从备份中加载 ${Object.keys(backupAnswers).length} 个正确答案`);
        }
    } catch (error) {
        console.log("加载备份考试结果失败：", error);
    }
    
    // 记录加载前的答案数量
    const beforeCount = Object.keys(qRightAnswer).length;
    console.log(`加载前已有 ${beforeCount} 个答案`);
    
    // 创建结果对象
    let result = {};
    
    // 首先加载当前已知的正确答案
    for (const q in qRightAnswer) {
        // 标准化问题文本，移除多余空格
        const normalizedQ = q.replace(/\s+/g, "");
        result[normalizedQ] = qRightAnswer[q];
    }
    
    // 然后使用备份中的正确答案
    for (const q in backupAnswers) {
        const normalizedQ = q.replace(/\s+/g, "");
        if (!result[normalizedQ]) {
            result[normalizedQ] = backupAnswers[q];
        }
    }
    
    // 如果有章节名称，从历史记录中加载该章节的答案
    if (qTitle !== "没有记录到章节名称") {
        const chapterAnswers = qAllAnswer[qTitle] || {};
        for (const q in chapterAnswers) {
            // 标准化问题文本，移除多余空格
            const normalizedQ = q.replace(/\s+/g, "");
            if (!result[normalizedQ]) { // 避免覆盖已有答案
                result[normalizedQ] = chapterAnswers[q];
            }
        }
    } else {
        console.log("没找到章节名称，无法从历史记录加载特定章节答案");
    }
    
    // 从所有章节加载答案
    for (const chapter in qAllAnswer) {
        const chapterAnswers = qAllAnswer[chapter] || {};
        for (const q in chapterAnswers) {
            // 标准化问题文本，移除多余空格
            const normalizedQ = q.replace(/\s+/g, "");
            if (!result[normalizedQ]) { // 避免覆盖已有答案
                result[normalizedQ] = chapterAnswers[q];
            }
        }
    }
    
    // 记录加载后的答案数量
    const afterCount = Object.keys(result).length;
    console.log(`从历史记录加载了 ${afterCount - beforeCount} 个答案，总计 ${afterCount} 个答案`);
    
    return result;
}

/**
 * 记录本次测试的正确答案
 * 从多个来源收集正确答案并合并到答案库中
 */
function saveRightAnwser() {
    // 获取现有数据
    const qRightAnswer = JSON.parse(localStorage.getItem(keyRightAnswer)) || {};
    const qTestAnswer = JSON.parse(localStorage.getItem(keyTestAnswer)) || {};
    const qWrongs = JSON.parse(localStorage.getItem(keyResult)) || {};
    const examResults = JSON.parse(localStorage.getItem("JJ_ExamResults")) || {};

    // 记录保存前的答案数量
    const beforeCount = Object.keys(qRightAnswer).length;
    console.log(`保存前正确答案库中有 ${beforeCount} 题`);
    
    // 首先尝试从备份加载正确答案
    try {
        const backupAnswers = JSON.parse(localStorage.getItem("JJ_ExamResultsBackup")) || {};
        if (Object.keys(backupAnswers).length > 0) {
            console.log(`从备份中加载 ${Object.keys(backupAnswers).length} 个正确答案`);
            // 将备份中的答案合并到正确答案库中
            for (const q in backupAnswers) {
                const normalizedQ = q.replace(/\s+/g, "");
                qRightAnswer[normalizedQ] = backupAnswers[q];
            }
            console.log(`已从备份更新正确答案库，当前共有 ${Object.keys(qRightAnswer).length} 个正确答案`);
        }
    } catch (error) {
        console.log("从备份加载正确答案失败：", error);
    }
    
    // 检查测试答案是否为空
    if (Object.keys(qTestAnswer).length === 0) {
        console.log("警告：本次测试答案为空");
        // 即使测试答案为空，也保存已加载的备份答案
        if (Object.keys(qRightAnswer).length > 0) {
            localStorage.setItem(keyRightAnswer, JSON.stringify(qRightAnswer));
        }
        return;
    }

    // 处理每个测试答案
    let correctCount = 0;
    let wrongCount = 0;
    
    for (const q in qTestAnswer) {
        // 标准化问题文本，移除多余空格
        const normalizedQ = q.replace(/\s+/g, "");
        
        if (!qWrongs.hasOwnProperty(normalizedQ) && !qWrongs.hasOwnProperty(q)) { // 正确的题目
            correctCount++;
            console.log(`正确的题目："${q.substring(0, 20)}..."，答案：${qTestAnswer[q]}`);
            qRightAnswer[normalizedQ] = qTestAnswer[q];
        } else {
            wrongCount++;
            console.log(`错误的题目："${q.substring(0, 20)}..."，答案：${qTestAnswer[q]}`);
        }
    }
    
    console.log(`本次测试：${correctCount} 题正确，${wrongCount} 题错误`);
    
    // 从考试结果中提取正确答案
    if (Object.keys(examResults).length > 0) {
        console.log(`从考试结果中提取正确答案`);
        // 遍历考试结果页面上的所有题目
        const questionItems = document.querySelectorAll(".state_cour_lis");
        if (questionItems && questionItems.length > 0) {
            let extractedCount = 0;
            questionItems.forEach((item, index) => {
                try {
                    // 获取问题文本
                    const questionElement = item.querySelector("p.state_lis_text");
                    if (!questionElement) return;
                    
                    const question = questionElement.getAttribute("title");
                    if (!question) return;
                    
                    // 标准化问题文本
                    const normalizedQ = question.replace(/\s+/g, "");
                    
                    // 检查该题是否正确（图片为bar_img表示正确）
                    const imgElement = item.querySelector("img");
                    if (imgElement && imgElement.src.includes("bar_img")) {
                        // 获取答案文本
                        const answerElements = item.querySelectorAll("p.state_lis_text");
                        if (answerElements.length < 2) return;
                        
                        const answer = answerElements[1].getAttribute("title");
                        if (!answer) return;
                        
                        // 清理答案文本
                        const cleanAnswer = answer.replace(/^【您的答案：\s*|\s*】$/g, "").trim();
                        if (!cleanAnswer) return;
                        
                        // 保存正确答案
                        qRightAnswer[normalizedQ] = cleanAnswer;
                        extractedCount++;
                    }
                } catch (error) {
                    console.log(`提取问题 ${index+1} 的正确答案时出错：${error}`);
                }
            });
            console.log(`从考试结果页面提取了 ${extractedCount} 个正确答案`);
        }
    }
    
    // 清理临时记录
    localStorage.removeItem(keyTestAnswer);
    
    // 保存正确答案
    if (Object.keys(qRightAnswer).length > 0) {
        localStorage.setItem(keyRightAnswer, JSON.stringify(qRightAnswer));
        
        // 记录保存后的答案数量
        const afterCount = Object.keys(qRightAnswer).length;
        console.log(`正确答案库：从 ${beforeCount} 题更新到 ${afterCount} 题（新增 ${afterCount - beforeCount} 题）`);
    } else {
        console.log("警告：保存后的正确答案库为空");
    }
}

/**
 * 清理缓存
 */
function cleanKeyStorage() {
    localStorage.removeItem(keyTest);
    localStorage.removeItem(keyResult);
    localStorage.removeItem(keyTestAnswer);
    localStorage.removeItem(keyRightAnswer);
}

/**
 * 查找答案选项
 * @param {string} qakey - 查询选择器
 * @param {number} index - 问题索引
 * @param {string} rightAnwserText - 正确答案文本
 * @returns {Element} 答案选项元素
 */
function findAnwser(qakey, index, rightAnwserText) {
    // 确保索引有效
    const elements = document.querySelectorAll(qakey);
    if (index < 0 || index >= elements.length) {
        console.log(`查找答案失败：索引 ${index} 超出范围 (0-${elements.length-1})`);
        return null;
    }
    
    const answerslist = elements[index];
    const arr = answerslist.getElementsByTagName("label");
    
    if (!arr || arr.length === 0) {
        console.log(`查找答案失败：问题 ${index} 没有选项`);
        return null;
    }
    
    // 尝试精确匹配
    for (let i = 0; i < arr.length; i++) {
        const text = arr[i].innerText.substring(3).trim(); // "A、"占用3个字符，去除空格
        
        if (text === rightAnwserText.trim()) {
            return arr[i];
        }
    }
    
    // 如果精确匹配失败，尝试模糊匹配（去除空格和标点符号）
    const normalizedAnswer = rightAnwserText.replace(/[\s.,，。、；;:：!！?？()（）\[\]【】]/g, "");
    
    for (let i = 0; i < arr.length; i++) {
        const text = arr[i].innerText.substring(3); // "A、"占用3个字符
        const normalizedText = text.replace(/[\s.,，。、；;:：!！?？()（）\[\]【】]/g, "");
        
        if (normalizedText === normalizedAnswer) {
            console.log(`模糊匹配成功："${text.substring(0, 20)}..."`);
            return arr[i];
        }
    }
    
    // 如果仍然失败，尝试包含匹配（检查答案是否包含在选项中，或选项是否包含在答案中）
    for (let i = 0; i < arr.length; i++) {
        const text = arr[i].innerText.substring(3); // "A、"占用3个字符
        const normalizedText = text.replace(/[\s.,，。、；;:：!！?？()（）\[\]【】]/g, "");
        
        if (normalizedText.includes(normalizedAnswer) || normalizedAnswer.includes(normalizedText)) {
            console.log(`包含匹配成功："${text.substring(0, 20)}..."`);
            return arr[i];
        }
    }
    
    console.log(`查找答案失败：找不到匹配 "${rightAnwserText.substring(0, 20)}..." 的选项`);
    return null;
}

/**
 * 获取选项字符编码
 * @param {string} an - 选项字母
 * @returns {number} 选项索引
 */
function getChoiceCode(an) {
    const charin = an || "A";
    return charin.charCodeAt(0) - "A".charCodeAt(0);
}

/**
 * 获取下一个选项字符
 * @param {string} an - 当前选项字母
 * @returns {string} 下一个选项字母
 */
function getNextChoice(an) {
    const code = an.charCodeAt(0) + 1;
    return String.fromCharCode(code);
}

// 监听来自弹出窗口的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    // 处理自动答题设置变更
    if (request.action === "autoAnswerChange") {
        autoAnswer = request.autoAnswer;
        console.log("收到自动答题设置变更消息：" + (autoAnswer ? "开启" : "关闭"));
        sendResponse({success: true});
    }
    // 处理显示答案请求
    else if (request.action === "showAnswers") {
        // 显示已记录答案
        const qAllAnswer = JSON.parse(localStorage.getItem(keyAllAnswer)) || {};
        const answersText = JSON.stringify(qAllAnswer, null, "\t");
        
        // 创建一个模态对话框显示答案
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            max-width: 80%;
            max-height: 80%;
            overflow: auto;
            position: relative;
        `;
        
        const closeBtn = document.createElement('button');
        closeBtn.textContent = '关闭';
        closeBtn.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            background-color: #f15854;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        `;
        closeBtn.onclick = function() {
            document.body.removeChild(modal);
        };
        
        const title = document.createElement('h2');
        title.textContent = '已记录答案';
        title.style.marginBottom = '15px';
        
        const textarea = document.createElement('textarea');
        textarea.value = answersText;
        textarea.style.cssText = `
            width: 100%;
            height: 400px;
            margin-bottom: 10px;
            font-family: monospace;
        `;
        
        const copyBtn = document.createElement('button');
        copyBtn.textContent = '复制答案';
        copyBtn.style.cssText = `
            padding: 5px 10px;
            background-color: #4cb0f9;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        `;
        copyBtn.onclick = function() {
            textarea.select();
            document.execCommand('copy');
            alert('答案已复制到剪贴板');
        };
        
        content.appendChild(closeBtn);
        content.appendChild(title);
        content.appendChild(textarea);
        content.appendChild(copyBtn);
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        sendResponse({success: true});
    } else if (request.action === "clearAnswers") {
        // 清除已记录答案
        localStorage.removeItem(keyAllAnswer);
        sendResponse({success: true});
    }
});

/**
 * 增强的重新考试函数
 * 确保可靠地点击重新考试按钮，支持多种选择器和重试机制
 */
function retryExam() {
    console.log("开始重新考试流程...");

    // 检查重试次数
    let retryCount = parseInt(localStorage.getItem(RETRY_COUNT_KEY)) || 0;
    console.log(`当前重试次数: ${retryCount}/${MAX_RETRY_COUNT}`);

    if (retryCount >= MAX_RETRY_COUNT) {
        console.log("已达到最大重试次数，停止自动重试");
        alert("已达到最大重试次数，请手动检查考试状态或联系管理员");
        return;
    }

    // 增加重试计数
    retryCount++;
    localStorage.setItem(RETRY_COUNT_KEY, retryCount.toString());

    // 延时执行重新考试，给页面处理时间
    setTimeout(function () {
        // 尝试多种方式查找重新考试按钮
        let retryButton = null;

        // 方法1：根据你提供的HTML结构查找
        retryButton = document.querySelector("input[type='button'][value='重新考试']");

        // 方法2：使用class选择器
        if (!retryButton) {
            retryButton = document.querySelector("input.state_foot_btn.state_edu[value='重新考试']");
        }

        // 方法3：使用jQuery选择器（如果jQuery可用）
        if (!retryButton && typeof $ !== 'undefined') {
            const jqButton = $("input[type=button][value='重新考试']");
            if (jqButton.length > 0) {
                retryButton = jqButton[0];
            }
        }

        // 方法4：查找包含"重新考试"文本的按钮
        if (!retryButton) {
            const allButtons = document.querySelectorAll("input[type='button'], button");
            for (let btn of allButtons) {
                if (btn.value === "重新考试" || btn.textContent === "重新考试") {
                    retryButton = btn;
                    break;
                }
            }
        }

        // 方法5：在state_foot容器中查找
        if (!retryButton) {
            const stateFootContainer = document.querySelector(".state_foot");
            if (stateFootContainer) {
                retryButton = stateFootContainer.querySelector("input[value='重新考试']");
            }
        }

        if (retryButton) {
            console.log("找到重新考试按钮，准备点击...");

            // 检查按钮是否有onclick事件
            if (retryButton.onclick) {
                console.log("按钮有onclick事件，直接调用");
                retryButton.onclick();
            } else if (retryButton.getAttribute('onclick')) {
                console.log("按钮有onclick属性，执行JavaScript代码");
                // 提取onclick属性中的JavaScript代码并执行
                const onclickCode = retryButton.getAttribute('onclick');
                try {
                    eval(onclickCode);
                } catch (error) {
                    console.log("执行onclick代码失败，尝试点击按钮：", error);
                    retryButton.click();
                }
            } else {
                console.log("直接点击按钮");
                retryButton.click();
            }

            // 添加额外的重试机制，如果3秒后还在当前页面，尝试其他方法
            setTimeout(function() {
                if (window.location.href.includes("exam_result.aspx")) {
                    console.log("3秒后仍在结果页面，尝试其他重新考试方法...");

                    // 尝试直接跳转到考试页面
                    const currentUrl = window.location.href;
                    const examUrl = currentUrl.replace("exam_result.aspx", "exam.aspx");

                    console.log("尝试直接跳转到考试页面：", examUrl);
                    window.location.href = examUrl;
                }
            }, 3000);

        } else {
            console.log("未找到重新考试按钮，尝试其他方法...");

            // 如果找不到按钮，尝试直接跳转到考试页面
            const currentUrl = window.location.href;
            if (currentUrl.includes("exam_result.aspx")) {
                const examUrl = currentUrl.replace("exam_result.aspx", "exam.aspx");
                console.log("直接跳转到考试页面：", examUrl);
                window.location.href = examUrl;
            } else {
                console.log("无法确定考试页面URL，请手动重新考试");
            }
        }

    }, (reTryTime + Math.ceil(Math.random() * randomX)));
}

/**
 * 检测考试未通过状态的增强函数
 * 支持多种检测方式，确保准确识别考试状态
 */
function detectExamFailure() {
    // 方法1：检查tips_text元素
    const tipsTextElements = document.querySelectorAll(".tips_text, p.tips_text");
    for (let element of tipsTextElements) {
        const text = element.innerText || element.textContent;
        if (text.includes("考试未通过") || text.includes("未通过") || text.includes("不合格")) {
            return true;
        }
    }

    // 方法2：检查state_tips容器中的失败图片
    const stateTips = document.querySelector(".state_tips");
    if (stateTips) {
        const failImg = stateTips.querySelector("img[src*='tips_fail']");
        if (failImg) {
            return true;
        }
    }

    // 方法3：检查是否存在重新考试按钮
    const retryButton = document.querySelector("input[value='重新考试']");
    if (retryButton) {
        return true;
    }

    return false;
}

// 页面加载完成后初始化
window.addEventListener('load', initialize);
